/**
 * 引擎服务
 * 负责连接编辑器与DL（Digital Learning）引擎
 */

// 导入引擎模块（忽略类型检查）
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import * as EngineModule from '../libs/dl-engine.mjs';

// 类型定义
interface EngineOptions {
  canvas?: HTMLCanvasElement | string;
  autoStart?: boolean;
  debug?: boolean;
  language?: string;
}

interface Engine {
  world: World;
  renderer: Renderer;
  assetManager: AssetManager;
  i18n: I18n;
  systems: System[];
  activeCamera: Camera | null;
  initialize(): void;
  start(): void;
  stop(): void;
  dispose(): void;
  isRunning(): boolean;
  addSystem(system: System): System;
  getSystem(type: string): System | null;
  removeSystem(system: System | string): boolean;
  getWorld(): World;
  getRenderer(): Renderer;
  getAssetManager(): AssetManager;
  getI18n(): I18n;
  setActiveCamera(camera: Camera): void;
  getActiveCamera(): Camera | null;
  setDebug(debug: boolean): void;
  isDebug(): boolean;
}

interface World {
  createScene(name?: string): Scene;
}

interface Scene {
  id: string;
  name: string;
  createEntity(name: string): Entity;
  removeEntity(entity: Entity): void;
  getEntities(): Entity[];
  addEntity(entity: Entity): void;
  clear(): void;
  dispose(): void;
}

interface Entity {
  id: string;
  name: string;
  hasComponent(type: string): boolean;
  getComponent(type: string): any;
  addComponent(type: string): any;
  getComponents(): Map<string, any>;
  isActive(): boolean;
  setParent(parent: Entity): void;
  getTransform(): any;
}

interface Camera {
  // 相机相关属性和方法
}

interface Vector3 {
  x: number;
  y: number;
  z: number;
}

interface Renderer {
  render(scene: any, camera: any): void;
  setSize(width: number, height: number): void;
  dispose(): void;
  domElement: HTMLCanvasElement;
  shadowMap: {
    enabled: boolean;
    type: string;
  };
  setPixelRatio(ratio: number): void;
  antialias: boolean;
}

interface AssetManager {
  initialize(): void;
  dispose(): void;
  loadModel(url: string, options?: any): Promise<any>;
  loadTexture(url: string, options?: any): Promise<any>;
  loadAudio(url: string, options?: any): Promise<any>;
  getAsset(id: string): any;
  hasAsset(id: string): boolean;
}

interface I18n {
  setLanguage(language: string): void;
  getLanguage(): string;
  translate(key: string, params?: any): string;
  t(key: string, params?: any): string;
}

interface System {
  initialize(): void;
  update(deltaTime: number): void;
  fixedUpdate(fixedDeltaTime: number): void;
  lateUpdate(deltaTime: number): void;
  dispose(): void;
  isEnabled(): boolean;
  setEnabled(enabled: boolean): void;
  getPriority(): number;
  getType(): string;
  setEngine(engine: Engine): void;
  setWorld(world: World): void;
}

interface PhysicsSystem extends System {
  enablePhysics(entityId: string, options?: any): void;
  disablePhysics(entityId: string): void;
  setGravity(gravity: Vector3): void;
  raycast(origin: Vector3, direction: Vector3, distance: number): any;
}

interface AnimationSystem extends System {
  playAnimation(entityId: string, animationName: string, options?: any): void;
  stopAnimation(entityId: string, animationName?: string): void;
  getAnimationState(entityId: string): any;
  createStateMachine(name: string): any;
  loadStateMachine(data: any): any;
}

interface AudioSystem extends System {
  playAudio(entityId: string, audioName: string, options?: any): void;
  stopAudio(entityId: string, audioName?: string): void;
  setMasterVolume(volume: number): void;
  getMasterVolume(): number;
}

interface InputSystem extends System {
  getInputManager(): InputManager;
  isKeyPressed(key: string): boolean;
  isMouseButtonPressed(button: number): boolean;
  getMousePosition(): Vector3;
}

interface InputManager {
  isKeyPressed(key: string): boolean;
  isMouseButtonPressed(button: number): boolean;
  getMousePosition(): Vector3;
  addInputMapping(mapping: any): void;
  removeInputMapping(id: string): void;
}

interface NetworkSystem extends System {
  connect(url: string): Promise<void>;
  disconnect(): void;
  sendMessage(type: string, data: any): void;
  isConnected(): boolean;
}

interface UISystem extends System {
  createUI(type: string, options?: any): any;
  removeUI(id: string): void;
  updateUI(id: string, data: any): void;
}

interface MaterialSystem extends System {
  createMaterial(type: string, options?: any): any;
  getMaterial(id: string): any;
  updateMaterial(id: string, properties: any): void;
}

// 从模块中提取构造函数和类
const {
  Engine,
  Vector3,
  PhysicsSystem,
  AnimationSystem,
  AudioSystem,
  InputSystem,
  NetworkSystem,
  UISystem,
  MaterialSystem,
  AssetManager,
  SceneManager,
  Time,
  Debug
} = EngineModule as any;

// 引擎事件类型
export enum EngineEventType {
  INITIALIZED = 'initialized',
  SCENE_LOADED = 'sceneLoaded',
  SCENE_UNLOADED = 'sceneUnloaded',
  OBJECT_SELECTED = 'objectSelected',
  OBJECT_DESELECTED = 'objectDeselected',
  OBJECT_ADDED = 'objectAdded',
  OBJECT_REMOVED = 'objectRemoved',
  OBJECT_CHANGED = 'objectChanged',
  TRANSFORM_CHANGED = 'transformChanged',
  COMPONENT_ADDED = 'componentAdded',
  COMPONENT_REMOVED = 'componentRemoved',
  COMPONENT_CHANGED = 'componentChanged',
  RENDER_FRAME = 'renderFrame',
}

// 选择模式
export enum SelectionMode {
  SINGLE = 'single',
  MULTIPLE = 'multiple',
  ADD = 'add',
  SUBTRACT = 'subtract',
}

// 变换模式
export enum TransformMode {
  TRANSLATE = 'translate',
  ROTATE = 'rotate',
  SCALE = 'scale',
}

// 坐标空间
export enum TransformSpace {
  LOCAL = 'local',
  WORLD = 'world',
}

// 引擎服务类
class EngineService {
  private static instance: EngineService;

  private engine: Engine | null = null;
  private activeScene: Scene | null = null;
  private activeCamera: Camera | null = null;
  private selectedEntities: Entity[] = [];
  private transformMode: TransformMode = TransformMode.TRANSLATE;
  private transformSpace: TransformSpace = TransformSpace.LOCAL;

  // 系统管理
  private physicsSystem: any = null;
  private animationSystem: any = null;
  private audioSystem: any = null;
  private inputSystem: any = null;
  private networkSystem: any = null;
  private uiSystem: any = null;
  private materialSystem: any = null;
  private renderSystem: any = null;

  // 管理器
  private assetManager: any = null;
  private sceneManager: any = null;

  // 事件系统
  private eventListeners: Map<string, Function[]> = new Map();

  private constructor() {
    // 初始化
  }

  /**
   * 添加事件监听器
   */
  public addEventListener(event: string, listener: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(listener);
  }

  /**
   * 移除事件监听器
   */
  public removeEventListener(event: string, listener: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   */
  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error('Error in engine service event listener:', error);
        }
      });
    }
  }

  /**
   * 移除所有事件监听器
   */
  private removeAllListeners(): void {
    this.eventListeners.clear();
  }

  /**
   * 获取引擎服务实例
   */
  public static getInstance(): EngineService {
    if (!EngineService.instance) {
      EngineService.instance = new EngineService();
    }
    return EngineService.instance;
  }

  /**
   * 初始化引擎
   * @param canvas 画布元素
   * @param options 引擎选项
   */
  public async initialize(canvas: HTMLCanvasElement, options: Partial<EngineOptions> = {}): Promise<void> {
    if (this.engine) {
      return;
    }

    try {
      // 创建引擎实例
      this.engine = new Engine({
        canvas,
        autoStart: false,
        ...options,
      });

      // 初始化引擎
      this.engine!.initialize();

      // 初始化系统
      await this.initializeSystems();

      // 发出初始化事件
      this.emit(EngineEventType.INITIALIZED, this.engine);

      console.log('引擎初始化成功');
    } catch (error) {
      console.error('引擎初始化失败:', error);
      throw error;
    }
  }

  /**
   * 初始化各个系统
   */
  private async initializeSystems(): Promise<void> {
    if (!this.engine) {
      throw new Error('引擎未初始化');
    }

    try {
      // 获取引擎的系统管理器
      const world = this.engine.getWorld();

      // 初始化物理系统
      this.physicsSystem = this.engine.getSystem('PhysicsSystem') || new PhysicsSystem();
      if (!this.engine.getSystem('PhysicsSystem')) {
        this.engine.addSystem(this.physicsSystem);
      }

      // 初始化动画系统
      this.animationSystem = this.engine.getSystem('AnimationSystem') || new AnimationSystem();
      if (!this.engine.getSystem('AnimationSystem')) {
        this.engine.addSystem(this.animationSystem);
      }

      // 初始化音频系统
      this.audioSystem = this.engine.getSystem('AudioSystem') || new AudioSystem();
      if (!this.engine.getSystem('AudioSystem')) {
        this.engine.addSystem(this.audioSystem);
      }

      // 初始化输入系统
      this.inputSystem = this.engine.getSystem('InputSystem') || new InputSystem();
      if (!this.engine.getSystem('InputSystem')) {
        this.engine.addSystem(this.inputSystem);
      }

      // 初始化网络系统
      this.networkSystem = this.engine.getSystem('NetworkSystem') || new NetworkSystem();
      if (!this.engine.getSystem('NetworkSystem')) {
        this.engine.addSystem(this.networkSystem);
      }

      // 初始化UI系统
      this.uiSystem = this.engine.getSystem('UISystem') || new UISystem();
      if (!this.engine.getSystem('UISystem')) {
        this.engine.addSystem(this.uiSystem);
      }

      // 初始化材质系统
      this.materialSystem = this.engine.getSystem('MaterialSystem') || new MaterialSystem();
      if (!this.engine.getSystem('MaterialSystem')) {
        this.engine.addSystem(this.materialSystem);
      }

      // 获取渲染系统（通常已经存在）
      this.renderSystem = this.engine.getSystem('RenderSystem');

      // 获取资源管理器
      this.assetManager = this.engine.getAssetManager();

      // 初始化场景管理器
      this.sceneManager = new SceneManager(world);

      console.log('所有系统初始化完成');
    } catch (error) {
      console.error('系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 启动引擎
   */
  public start(): void {
    if (!this.engine) {
      throw new Error('引擎未初始化');
    }

    this.engine.start();
  }

  /**
   * 停止引擎
   */
  public stop(): void {
    if (!this.engine) {
      throw new Error('引擎未初始化');
    }

    this.engine.stop();
  }

  /**
   * 加载场景
   * @param sceneData 场景数据
   */
  public async loadScene(sceneData: any): Promise<Scene> {
    if (!this.engine) {
      throw new Error('引擎未初始化');
    }

    try {
      // 卸载当前场景
      if (this.activeScene) {
        await this.unloadScene();
      }

      // 创建新场景
      this.activeScene = this.engine.world.createScene();

      // 从数据加载场景
      if (sceneData) {
        await this.loadSceneFromData(sceneData);
      }

      // 设置活动相机
      const entities = this.activeScene.getEntities();
      const cameraEntities = entities.filter((entity: Entity) => entity.hasComponent('Camera'));

      if (cameraEntities.length > 0) {
        this.activeCamera = cameraEntities[0].getComponent('Camera') as Camera;
      } else {
        // 创建默认相机
        const cameraEntity = this.activeScene.createEntity('Main Camera');
        const transform = cameraEntity.getTransform();
        transform.setPosition(new Vector3(0, 5, 10));
        transform.lookAt(new Vector3(0, 0, 0));

        this.activeCamera = cameraEntity.addComponent('Camera') as Camera;
      }

      // 发出场景加载事件
      this.emit(EngineEventType.SCENE_LOADED, this.activeScene);

      console.log('场景加载成功');
      return this.activeScene;
    } catch (error) {
      console.error('场景加载失败:', error);
      throw error;
    }
  }

  /**
   * 卸载当前场景
   */
  public async unloadScene(): Promise<void> {
    if (!this.engine || !this.activeScene) {
      return;
    }

    try {
      // 清除选中的实体
      this.clearSelection();

      // 发出场景卸载事件
      this.emit(EngineEventType.SCENE_UNLOADED, this.activeScene);

      // 销毁场景
      this.activeScene.dispose();
      this.activeScene = null;
      this.activeCamera = null;

      console.log('场景卸载成功');
    } catch (error) {
      console.error('场景卸载失败:', error);
      throw error;
    }
  }

  /**
   * 保存当前场景
   * @returns 场景数据
   */
  public async saveScene(): Promise<any> {
    if (!this.engine || !this.activeScene) {
      throw new Error('没有活动场景');
    }

    try {
      // 序列化场景
      const sceneData = await this.serializeScene();
      console.log('场景保存成功');
      return sceneData;
    } catch (error) {
      console.error('场景保存失败:', error);
      throw error;
    }
  }

  /**
   * 选择实体
   * @param entity 实体
   * @param mode 选择模式
   */
  public selectEntity(entity: Entity, mode: SelectionMode = SelectionMode.SINGLE): void {
    if (!entity) {
      return;
    }

    switch (mode) {
      case SelectionMode.SINGLE:
        // 清除当前选择
        this.clearSelection();
        // 添加新选择
        this.selectedEntities.push(entity);
        this.emit(EngineEventType.OBJECT_SELECTED, entity);
        break;

      case SelectionMode.MULTIPLE:
      case SelectionMode.ADD:
        // 如果实体不在选择列表中，添加它
        if (!this.selectedEntities.includes(entity)) {
          this.selectedEntities.push(entity);
          this.emit(EngineEventType.OBJECT_SELECTED, entity);
        }
        break;

      case SelectionMode.SUBTRACT:
        // 从选择列表中移除实体
        const index = this.selectedEntities.indexOf(entity);
        if (index !== -1) {
          this.selectedEntities.splice(index, 1);
          this.emit(EngineEventType.OBJECT_DESELECTED, entity);
        }
        break;
    }
  }

  /**
   * 取消选择实体
   * @param entity 实体，如果为空则取消所有选择
   */
  public deselectEntity(entity?: Entity): void {
    if (!entity) {
      this.clearSelection();
      return;
    }

    const index = this.selectedEntities.indexOf(entity);
    if (index !== -1) {
      this.selectedEntities.splice(index, 1);
      this.emit(EngineEventType.OBJECT_DESELECTED, entity);
    }
  }

  /**
   * 清除所有选择
   */
  public clearSelection(): void {
    const entities = [...this.selectedEntities];
    this.selectedEntities = [];

    for (const entity of entities) {
      this.emit(EngineEventType.OBJECT_DESELECTED, entity);
    }
  }

  /**
   * 获取选中的实体
   */
  public getSelectedEntities(): Entity[] {
    return [...this.selectedEntities];
  }

  /**
   * 设置变换模式
   * @param mode 变换模式
   */
  public setTransformMode(mode: TransformMode): void {
    this.transformMode = mode;
  }

  /**
   * 获取变换模式
   */
  public getTransformMode(): TransformMode {
    return this.transformMode;
  }

  /**
   * 设置变换空间
   * @param space 变换空间
   */
  public setTransformSpace(space: TransformSpace): void {
    this.transformSpace = space;
  }

  /**
   * 获取变换空间
   */
  public getTransformSpace(): TransformSpace {
    return this.transformSpace;
  }

  /**
   * 创建实体
   * @param name 实体名称
   * @param parent 父实体
   */
  public createEntity(name: string, parent?: Entity): Entity {
    if (!this.activeScene) {
      throw new Error('没有活动场景');
    }

    const entity = this.activeScene.createEntity(name);
    if (parent) {
      entity.setParent(parent);
    }
    this.emit(EngineEventType.OBJECT_ADDED, entity);
    return entity;
  }

  /**
   * 删除实体
   * @param entity 实体
   */
  public removeEntity(entity: Entity): void {
    if (!this.activeScene) {
      throw new Error('没有活动场景');
    }

    // 如果实体被选中，取消选择
    this.deselectEntity(entity);

    // 删除实体
    this.activeScene.removeEntity(entity);
    this.emit(EngineEventType.OBJECT_REMOVED, entity);
  }

  /**
   * 获取引擎实例
   */
  public getEngine(): Engine | null {
    return this.engine;
  }

  /**
   * 获取活动场景
   */
  public getActiveScene(): Scene | null {
    return this.activeScene;
  }

  /**
   * 获取活动相机
   */
  public getActiveCamera(): Camera | null {
    return this.activeCamera;
  }

  /**
   * 设置活动相机
   * @param camera 相机
   */
  public setActiveCamera(camera: Camera): void {
    this.activeCamera = camera;
  }

  /**
   * 获取物理系统
   * @returns 物理系统实例
   */
  public getPhysicsSystem(): any {
    return this.physicsSystem;
  }

  /**
   * 获取动画系统
   * @returns 动画系统实例
   */
  public getAnimationSystem(): any {
    return this.animationSystem;
  }

  /**
   * 获取音频系统
   * @returns 音频系统实例
   */
  public getAudioSystem(): any {
    return this.audioSystem;
  }

  /**
   * 获取输入系统
   * @returns 输入系统实例
   */
  public getInputSystem(): any {
    return this.inputSystem;
  }

  /**
   * 获取网络系统
   * @returns 网络系统实例
   */
  public getNetworkSystem(): any {
    return this.networkSystem;
  }

  /**
   * 获取UI系统
   * @returns UI系统实例
   */
  public getUISystem(): any {
    return this.uiSystem;
  }

  /**
   * 获取材质系统
   * @returns 材质系统实例
   */
  public getMaterialSystem(): any {
    return this.materialSystem;
  }

  /**
   * 获取渲染系统
   * @returns 渲染系统实例
   */
  public getRenderSystem(): any {
    return this.renderSystem;
  }

  /**
   * 获取资源管理器
   * @returns 资源管理器实例
   */
  public getAssetManager(): any {
    return this.assetManager;
  }

  /**
   * 获取场景管理器
   * @returns 场景管理器实例
   */
  public getSceneManager(): any {
    return this.sceneManager;
  }

  /**
   * 获取时间系统
   * @returns 时间相关信息
   */
  public getTime(): any {
    return {
      getTime: () => Time.getTime(),
      getDeltaTime: () => Time.getDeltaTime(),
      getFrameCount: () => Time.getFrameCount(),
      getFPS: () => Time.getFPS(),
      getTimeScale: () => Time.getTimeScale(),
      setTimeScale: (scale: number) => Time.setTimeScale(scale)
    };
  }

  /**
   * 获取调试工具
   * @returns 调试工具实例
   */
  public getDebug(): any {
    return Debug;
  }

  /**
   * 启用物理调试
   * @param enabled 是否启用
   */
  public setPhysicsDebug(enabled: boolean): void {
    if (this.physicsSystem && typeof this.physicsSystem.setDebugEnabled === 'function') {
      this.physicsSystem.setDebugEnabled(enabled);
    }
  }

  /**
   * 设置渲染质量
   * @param quality 质量等级 ('low' | 'medium' | 'high' | 'ultra')
   */
  public setRenderQuality(quality: string): void {
    if (this.renderSystem && typeof this.renderSystem.setQuality === 'function') {
      this.renderSystem.setQuality(quality);
    }
  }

  /**
   * 连接到网络服务器
   * @param url 服务器地址
   * @returns 连接Promise
   */
  public async connectToServer(url: string): Promise<void> {
    if (this.networkSystem && typeof this.networkSystem.connect === 'function') {
      return this.networkSystem.connect(url);
    }
    throw new Error('网络系统未初始化');
  }

  /**
   * 断开网络连接
   */
  public disconnectFromServer(): void {
    if (this.networkSystem && typeof this.networkSystem.disconnect === 'function') {
      this.networkSystem.disconnect();
    }
  }

  /**
   * 发送网络消息
   * @param type 消息类型
   * @param data 消息数据
   */
  public sendNetworkMessage(type: string, data: any): void {
    if (this.networkSystem && typeof this.networkSystem.sendMessage === 'function') {
      this.networkSystem.sendMessage(type, data);
    }
  }

  /**
   * 创建材质
   * @param type 材质类型
   * @param options 材质选项
   * @returns 创建的材质
   */
  public createMaterial(type: string, options: any = {}): any {
    if (this.materialSystem && typeof this.materialSystem.createMaterial === 'function') {
      return this.materialSystem.createMaterial(type, options);
    }
    return null;
  }

  /**
   * 播放音频
   * @param entityId 实体ID
   * @param audioName 音频名称
   * @param options 播放选项
   */
  public playAudio(entityId: string, audioName: string, options: any = {}): void {
    if (this.audioSystem && typeof this.audioSystem.playAudio === 'function') {
      this.audioSystem.playAudio(entityId, audioName, options);
    }
  }

  /**
   * 停止音频
   * @param entityId 实体ID
   * @param audioName 音频名称（可选）
   */
  public stopAudio(entityId: string, audioName?: string): void {
    if (this.audioSystem && typeof this.audioSystem.stopAudio === 'function') {
      this.audioSystem.stopAudio(entityId, audioName);
    }
  }

  /**
   * 设置主音量
   * @param volume 音量值 (0-1)
   */
  public setMasterVolume(volume: number): void {
    if (this.audioSystem && typeof this.audioSystem.setMasterVolume === 'function') {
      this.audioSystem.setMasterVolume(volume);
    }
  }

  /**
   * 销毁引擎服务
   */
  public dispose(): void {
    if (this.engine) {
      this.engine.dispose();
      this.engine = null;
    }

    // 清理系统引用
    this.physicsSystem = null;
    this.animationSystem = null;
    this.audioSystem = null;
    this.inputSystem = null;
    this.networkSystem = null;
    this.uiSystem = null;
    this.materialSystem = null;
    this.renderSystem = null;
    this.assetManager = null;
    this.sceneManager = null;

    this.activeScene = null;
    this.activeCamera = null;
    this.selectedEntities = [];

    this.removeAllListeners();
  }

  /**
   * 加载3D模型
   * @param url 模型文件URL
   * @param options 加载选项
   * @returns 加载的模型
   */
  public async loadModel(url: string, options: any = {}): Promise<any> {
    if (this.assetManager && typeof this.assetManager.loadModel === 'function') {
      return this.assetManager.loadModel(url, options);
    }
    throw new Error('资源管理器未初始化');
  }

  /**
   * 加载纹理
   * @param url 纹理文件URL
   * @param options 加载选项
   * @returns 加载的纹理
   */
  public async loadTexture(url: string, options: any = {}): Promise<any> {
    if (this.assetManager && typeof this.assetManager.loadTexture === 'function') {
      return this.assetManager.loadTexture(url, options);
    }
    throw new Error('资源管理器未初始化');
  }

  /**
   * 加载音频
   * @param url 音频文件URL
   * @param options 加载选项
   * @returns 加载的音频
   */
  public async loadAudio(url: string, options: any = {}): Promise<any> {
    if (this.assetManager && typeof this.assetManager.loadAudio === 'function') {
      return this.assetManager.loadAudio(url, options);
    }
    throw new Error('资源管理器未初始化');
  }

  /**
   * 检查按键是否按下
   * @param key 按键名称
   * @returns 是否按下
   */
  public isKeyPressed(key: string): boolean {
    if (this.inputSystem && typeof this.inputSystem.isKeyPressed === 'function') {
      return this.inputSystem.isKeyPressed(key);
    }
    return false;
  }

  /**
   * 检查鼠标按钮是否按下
   * @param button 鼠标按钮 (0=左键, 1=中键, 2=右键)
   * @returns 是否按下
   */
  public isMouseButtonPressed(button: number): boolean {
    if (this.inputSystem && typeof this.inputSystem.isMouseButtonPressed === 'function') {
      return this.inputSystem.isMouseButtonPressed(button);
    }
    return false;
  }

  /**
   * 获取鼠标位置
   * @returns 鼠标位置
   */
  public getMousePosition(): Vector3 {
    if (this.inputSystem && typeof this.inputSystem.getMousePosition === 'function') {
      return this.inputSystem.getMousePosition();
    }
    return { x: 0, y: 0, z: 0 };
  }

  /**
   * 创建UI元素
   * @param type UI类型
   * @param options UI选项
   * @returns 创建的UI元素
   */
  public createUI(type: string, options: any = {}): any {
    if (this.uiSystem && typeof this.uiSystem.createUI === 'function') {
      return this.uiSystem.createUI(type, options);
    }
    return null;
  }

  /**
   * 移除UI元素
   * @param id UI元素ID
   */
  public removeUI(id: string): void {
    if (this.uiSystem && typeof this.uiSystem.removeUI === 'function') {
      this.uiSystem.removeUI(id);
    }
  }

  /**
   * 更新UI元素
   * @param id UI元素ID
   * @param data 更新数据
   */
  public updateUI(id: string, data: any): void {
    if (this.uiSystem && typeof this.uiSystem.updateUI === 'function') {
      this.uiSystem.updateUI(id, data);
    }
  }

  /**
   * 启用实体物理
   * @param entityId 实体ID
   * @param options 物理选项
   */
  public enableEntityPhysics(entityId: string, options: any = {}): void {
    if (this.physicsSystem && typeof this.physicsSystem.enablePhysics === 'function') {
      this.physicsSystem.enablePhysics(entityId, options);
    }
  }

  /**
   * 禁用实体物理
   * @param entityId 实体ID
   */
  public disableEntityPhysics(entityId: string): void {
    if (this.physicsSystem && typeof this.physicsSystem.disablePhysics === 'function') {
      this.physicsSystem.disablePhysics(entityId);
    }
  }

  /**
   * 设置重力
   * @param gravity 重力向量
   */
  public setGravity(gravity: Vector3): void {
    if (this.physicsSystem && typeof this.physicsSystem.setGravity === 'function') {
      this.physicsSystem.setGravity(gravity);
    }
  }

  /**
   * 射线检测
   * @param origin 起点
   * @param direction 方向
   * @param distance 距离
   * @returns 检测结果
   */
  public raycast(origin: Vector3, direction: Vector3, distance: number): any {
    if (this.physicsSystem && typeof this.physicsSystem.raycast === 'function') {
      return this.physicsSystem.raycast(origin, direction, distance);
    }
    return null;
  }

  /**
   * 播放实体动画
   * @param entityId 实体ID
   * @param animationName 动画名称
   * @param options 播放选项
   */
  public playEntityAnimation(entityId: string, animationName: string, options: any = {}): void {
    if (this.animationSystem && typeof this.animationSystem.playAnimation === 'function') {
      this.animationSystem.playAnimation(entityId, animationName, options);
    }
  }

  /**
   * 停止实体动画
   * @param entityId 实体ID
   * @param animationName 动画名称（可选）
   */
  public stopEntityAnimation(entityId: string, animationName?: string): void {
    if (this.animationSystem && typeof this.animationSystem.stopAnimation === 'function') {
      this.animationSystem.stopAnimation(entityId, animationName);
    }
  }

  /**
   * 获取实体动画状态
   * @param entityId 实体ID
   * @returns 动画状态
   */
  public getEntityAnimationState(entityId: string): any {
    if (this.animationSystem && typeof this.animationSystem.getAnimationState === 'function') {
      return this.animationSystem.getAnimationState(entityId);
    }
    return null;
  }

  /**
   * 创建动画状态机
   * @param name 状态机名称
   * @returns 创建的状态机
   */
  public createAnimationStateMachine(name: string): any {
    if (this.animationSystem && typeof this.animationSystem.createStateMachine === 'function') {
      return this.animationSystem.createStateMachine(name);
    }
    return null;
  }

  /**
   * 加载动画状态机
   * @param data 状态机数据
   * @returns 加载的状态机
   */
  public loadAnimationStateMachine(data: any): any {
    if (this.animationSystem && typeof this.animationSystem.loadStateMachine === 'function') {
      return this.animationSystem.loadStateMachine(data);
    }
    return null;
  }

  /**
   * 调用引擎方法
   * @param method 方法名称
   * @param args 参数
   * @returns 返回值
   */
  public async callEngineMethod(method: string, ...args: any[]): Promise<any> {
    if (!this.engine) {
      throw new Error('引擎未初始化');
    }

    try {
      // 实际调用引擎方法
      console.log(`调用引擎方法: ${method}`, args);

      // 根据方法名调用相应的引擎功能
      const result = await this.executeEngineMethod(method, args);
      if (result !== undefined) {
        return result;
      }

      // 如果没有找到对应的方法，返回模拟数据
      if (method === 'getAvailableAnimationClips') {
        return ['idle', 'walk', 'run', 'jump', 'attack', 'death'];
      } else if (method === 'getStateMachineList') {
        return ['主状态机', '战斗状态机', '移动状态机'];
      } else if (method === 'loadStateMachine') {
        return {
          states: [
            {
              name: '空闲',
              type: 'SingleAnimationState',
              clipName: 'idle',
              loop: true,
              clamp: false,
              position: { x: 100, y: 100 }
            },
            {
              name: '行走',
              type: 'SingleAnimationState',
              clipName: 'walk',
              loop: true,
              clamp: false,
              position: { x: 300, y: 100 }
            },
            {
              name: '跑步',
              type: 'SingleAnimationState',
              clipName: 'run',
              loop: true,
              clamp: false,
              position: { x: 500, y: 100 }
            }
          ],
          transitions: [
            {
              from: '空闲',
              to: '行走',
              conditionExpression: 'stateMachine.getParameter("speed") > 0.1',
              duration: 0.3,
              canInterrupt: true
            },
            {
              from: '行走',
              to: '空闲',
              conditionExpression: 'stateMachine.getParameter("speed") <= 0.1',
              duration: 0.3,
              canInterrupt: true
            },
            {
              from: '行走',
              to: '跑步',
              conditionExpression: 'stateMachine.getParameter("speed") > 1.0',
              duration: 0.3,
              canInterrupt: true
            },
            {
              from: '跑步',
              to: '行走',
              conditionExpression: 'stateMachine.getParameter("speed") <= 1.0',
              duration: 0.3,
              canInterrupt: true
            }
          ],
          parameters: [
            {
              name: 'speed',
              type: 'number',
              defaultValue: 0,
              minValue: 0,
              maxValue: 10
            },
            {
              name: 'isJumping',
              type: 'boolean',
              defaultValue: false
            }
          ],
          currentState: '空闲'
        };
      } else if (method === 'createStateMachine') {
        return {
          name: '新状态机',
          states: [
            {
              name: '空闲',
              type: 'SingleAnimationState',
              clipName: 'idle',
              loop: true,
              clamp: false,
              position: { x: 100, y: 100 }
            }
          ],
          transitions: [],
          parameters: [],
          currentState: '空闲'
        };
      } else if (method === 'getStateMachineDebugInfo') {
        return {
          events: [
            {
              type: 'stateEnter',
              time: 0,
              data: { state: { name: '空闲', type: 'SingleAnimationState' } }
            },
            {
              type: 'parameterChange',
              time: 1.5,
              data: { name: 'speed', oldValue: 0, newValue: 0.5 }
            },
            {
              type: 'conditionEvaluate',
              time: 1.5,
              data: { expression: 'stateMachine.getParameter("speed") > 0.1', result: true }
            },
            {
              type: 'transitionStart',
              time: 1.5,
              data: { transition: { from: '空闲', to: '行走' } }
            },
            {
              type: 'stateExit',
              time: 1.8,
              data: { state: { name: '空闲', type: 'SingleAnimationState' } }
            },
            {
              type: 'stateEnter',
              time: 1.8,
              data: { state: { name: '行走', type: 'SingleAnimationState' } }
            },
            {
              type: 'transitionEnd',
              time: 1.8,
              data: { transition: { from: '空闲', to: '行走' } }
            }
          ],
          parameters: [
            { name: 'speed', type: 'number', value: 0.5 },
            { name: 'isJumping', type: 'boolean', value: false }
          ],
          currentState: {
            name: '行走',
            type: 'SingleAnimationState',
            clipName: 'walk',
            loop: true,
            clamp: false
          }
        };
      }

      // 默认返回成功
      return true;
    } catch (error) {
      console.error(`调用引擎方法失败: ${method}`, error);
      throw error;
    }
  }

  /**
   * 执行引擎方法
   * @param method 方法名
   * @param args 参数
   * @returns 执行结果
   */
  private async executeEngineMethod(method: string, args: any[]): Promise<any> {
    if (!this.engine) {
      throw new Error('引擎未初始化');
    }

    try {
      switch (method) {
        // 场景相关方法
        case 'createEntity':
          return this.handleCreateEntity(args);
        case 'removeEntity':
          return this.handleRemoveEntity(args);
        case 'findEntity':
          return this.handleFindEntity(args);
        case 'getEntities':
          return this.handleGetEntities();

        // 组件相关方法
        case 'addComponent':
          return this.handleAddComponent(args);
        case 'removeComponent':
          return this.handleRemoveComponent(args);
        case 'getComponent':
          return this.handleGetComponent(args);

        // 资源相关方法
        case 'loadModel':
          return this.handleLoadModel(args);
        case 'loadTexture':
          return this.handleLoadTexture(args);
        case 'loadAudio':
          return this.handleLoadAudio(args);

        // 动画相关方法
        case 'playAnimation':
          return this.handlePlayAnimation(args);
        case 'stopAnimation':
          return this.handleStopAnimation(args);
        case 'getAnimationState':
          return this.handleGetAnimationState(args);

        // 物理相关方法
        case 'enablePhysics':
          return this.handleEnablePhysics(args);
        case 'disablePhysics':
          return this.handleDisablePhysics(args);
        case 'setGravity':
          return this.handleSetGravity(args);

        // 渲染相关方法
        case 'setRenderQuality':
          return this.handleSetRenderQuality(args);
        case 'enableShadows':
          return this.handleEnableShadows(args);
        case 'setLighting':
          return this.handleSetLighting(args);

        default:
          // 未找到对应方法
          return undefined;
      }
    } catch (error) {
      console.error(`执行引擎方法失败: ${method}`, error);
      throw error;
    }
  }

  // 场景相关方法实现
  private async handleCreateEntity(args: any[]): Promise<Entity> {
    const [name, parentId] = args;
    const parent = parentId ? this.findEntityById(parentId) || undefined : undefined;
    return this.createEntity(name || 'Entity', parent);
  }

  private async handleRemoveEntity(args: any[]): Promise<void> {
    const [entityId] = args;
    const entity = this.findEntityById(entityId);
    if (entity) {
      this.removeEntity(entity);
    }
  }

  private async handleFindEntity(args: any[]): Promise<Entity | null> {
    const [entityId] = args;
    return this.findEntityById(entityId);
  }

  private async handleGetEntities(): Promise<Entity[]> {
    return this.activeScene ? this.activeScene.getEntities() : [];
  }

  // 组件相关方法实现
  private async handleAddComponent(args: any[]): Promise<any> {
    const [entityId, componentType, properties] = args;
    const entity = this.findEntityById(entityId);
    if (entity) {
      const component = entity.addComponent(componentType);
      if (properties && component) {
        Object.assign(component, properties);
      }
      return component;
    }
    return null;
  }

  private async handleRemoveComponent(args: any[]): Promise<void> {
    const [entityId, componentType] = args;
    const entity = this.findEntityById(entityId);
    if (entity && entity.hasComponent(componentType)) {
      // 这里需要引擎支持移除组件的方法
      console.log(`移除组件: ${componentType} from entity: ${entityId}`);
    }
  }

  private async handleGetComponent(args: any[]): Promise<any> {
    const [entityId, componentType] = args;
    const entity = this.findEntityById(entityId);
    return entity ? entity.getComponent(componentType) : null;
  }

  // 资源相关方法实现
  private async handleLoadModel(args: any[]): Promise<any> {
    const [url, options] = args;
    console.log(`加载模型: ${url}`, options);

    try {
      // 调用引擎的资源加载器
      if (this.engine && (this.engine as any).resourceLoader) {
        const result = await (this.engine as any).resourceLoader.loadModel(url, options);
        return result;
      }

      // 如果引擎不支持，返回模拟结果
      return {
        url,
        loaded: true,
        timestamp: Date.now(),
        type: 'model',
        vertices: 1000,
        faces: 500
      };
    } catch (error) {
      console.error('加载模型失败:', error);
      throw error;
    }
  }

  private async handleLoadTexture(args: any[]): Promise<any> {
    const [url, options] = args;
    console.log(`加载纹理: ${url}`, options);

    try {
      // 调用引擎的纹理加载器
      if (this.engine && (this.engine as any).textureLoader) {
        const result = await (this.engine as any).textureLoader.load(url, options);
        return result;
      }

      // 如果引擎不支持，返回模拟结果
      return {
        url,
        loaded: true,
        timestamp: Date.now(),
        type: 'texture',
        width: 512,
        height: 512,
        format: 'RGBA'
      };
    } catch (error) {
      console.error('加载纹理失败:', error);
      throw error;
    }
  }

  private async handleLoadAudio(args: any[]): Promise<any> {
    const [url, options] = args;
    console.log(`加载音频: ${url}`, options);

    try {
      // 调用引擎的音频加载器
      if (this.engine && (this.engine as any).audioLoader) {
        const result = await (this.engine as any).audioLoader.load(url, options);
        return result;
      }

      // 如果引擎不支持，返回模拟结果
      return {
        url,
        loaded: true,
        timestamp: Date.now(),
        type: 'audio',
        duration: 30.5,
        channels: 2,
        sampleRate: 44100
      };
    } catch (error) {
      console.error('加载音频失败:', error);
      throw error;
    }
  }

  // 动画相关方法实现
  private async handlePlayAnimation(args: any[]): Promise<void> {
    const [entityId, animationName, options] = args;
    const entity = this.findEntityById(entityId);
    if (entity) {
      console.log(`播放动画: ${animationName} on entity: ${entityId}`, options);

      try {
        // 获取动画组件
        const animationComponent = entity.getComponent('Animation');
        if (animationComponent && typeof animationComponent.play === 'function') {
          animationComponent.play(animationName, options);
        } else {
          // 如果没有动画组件，尝试直接调用引擎方法
          if (this.engine && (this.engine as any).animationSystem) {
            (this.engine as any).animationSystem.playAnimation(entityId, animationName, options);
          }
        }

        // 发出动画事件
        this.emit('animationStarted', { entityId, animationName, options });
      } catch (error) {
        console.error('播放动画失败:', error);
        throw error;
      }
    }
  }

  private async handleStopAnimation(args: any[]): Promise<void> {
    const [entityId, animationName] = args;
    const entity = this.findEntityById(entityId);
    if (entity) {
      console.log(`停止动画: ${animationName} on entity: ${entityId}`);

      try {
        // 获取动画组件
        const animationComponent = entity.getComponent('Animation');
        if (animationComponent && typeof animationComponent.stop === 'function') {
          animationComponent.stop(animationName);
        } else {
          // 如果没有动画组件，尝试直接调用引擎方法
          if (this.engine && (this.engine as any).animationSystem) {
            (this.engine as any).animationSystem.stopAnimation(entityId, animationName);
          }
        }

        // 发出动画事件
        this.emit('animationStopped', { entityId, animationName });
      } catch (error) {
        console.error('停止动画失败:', error);
        throw error;
      }
    }
  }

  private async handleGetAnimationState(args: any[]): Promise<any> {
    const [entityId] = args;
    const entity = this.findEntityById(entityId);
    if (entity) {
      // 返回动画状态信息
      return {
        isPlaying: false,
        currentAnimation: null,
        time: 0,
        duration: 0
      };
    }
    return null;
  }

  // 物理相关方法实现
  private async handleEnablePhysics(args: any[]): Promise<void> {
    const [entityId, options] = args;
    console.log(`启用物理: entity: ${entityId}`, options);

    try {
      const entity = this.findEntityById(entityId);
      if (entity) {
        // 添加或获取物理组件
        let physicsComponent = entity.getComponent('Physics');
        if (!physicsComponent) {
          physicsComponent = entity.addComponent('Physics');
        }

        // 设置物理属性
        if (physicsComponent && options) {
          Object.assign(physicsComponent, options);
        }

        // 调用引擎物理系统
        if (this.engine && (this.engine as any).physicsWorld) {
          (this.engine as any).physicsWorld.addBody(entityId, options);
        }

        // 发出物理事件
        this.emit('physicsEnabled', { entityId, options });
      }
    } catch (error) {
      console.error('启用物理失败:', error);
      throw error;
    }
  }

  private async handleDisablePhysics(args: any[]): Promise<void> {
    const [entityId] = args;
    console.log(`禁用物理: entity: ${entityId}`);

    try {
      const entity = this.findEntityById(entityId);
      if (entity) {
        // 移除物理组件
        const physicsComponent = entity.getComponent('Physics');
        if (physicsComponent && typeof physicsComponent.setEnabled === 'function') {
          physicsComponent.setEnabled(false);
        }

        // 调用引擎物理系统
        if (this.engine && (this.engine as any).physicsWorld) {
          (this.engine as any).physicsWorld.removeBody(entityId);
        }

        // 发出物理事件
        this.emit('physicsDisabled', { entityId });
      }
    } catch (error) {
      console.error('禁用物理失败:', error);
      throw error;
    }
  }

  private async handleSetGravity(args: any[]): Promise<void> {
    const [gravity] = args;
    console.log(`设置重力:`, gravity);

    try {
      // 调用引擎物理系统设置重力
      if (this.engine && (this.engine as any).physicsWorld) {
        (this.engine as any).physicsWorld.setGravity(gravity);
      }

      // 发出重力变更事件
      this.emit('gravityChanged', { gravity });
    } catch (error) {
      console.error('设置重力失败:', error);
      throw error;
    }
  }

  // 渲染相关方法实现
  private async handleSetRenderQuality(args: any[]): Promise<void> {
    const [quality] = args;
    console.log(`设置渲染质量: ${quality}`);

    try {
      // 调用引擎渲染器设置质量
      if (this.engine && (this.engine as any).renderer) {
        const renderer = (this.engine as any).renderer;

        // 根据质量等级设置不同的渲染参数
        switch (quality) {
          case 'low':
            renderer.setPixelRatio(0.5);
            renderer.shadowMap.enabled = false;
            break;
          case 'medium':
            renderer.setPixelRatio(0.75);
            renderer.shadowMap.enabled = true;
            break;
          case 'high':
            renderer.setPixelRatio(1.0);
            renderer.shadowMap.enabled = true;
            renderer.antialias = true;
            break;
          case 'ultra':
            renderer.setPixelRatio(window.devicePixelRatio || 1);
            renderer.shadowMap.enabled = true;
            renderer.antialias = true;
            break;
        }
      }

      // 发出渲染质量变更事件
      this.emit('renderQualityChanged', { quality });
    } catch (error) {
      console.error('设置渲染质量失败:', error);
      throw error;
    }
  }

  private async handleEnableShadows(args: any[]): Promise<void> {
    const [enabled] = args;
    console.log(`${enabled ? '启用' : '禁用'}阴影`);

    try {
      // 调用引擎渲染器设置阴影
      if (this.engine && (this.engine as any).renderer) {
        const renderer = (this.engine as any).renderer;
        renderer.shadowMap.enabled = enabled;

        if (enabled) {
          // 设置阴影类型和参数
          renderer.shadowMap.type = 'PCFSoftShadowMap'; // 或其他阴影类型
        }
      }

      // 更新场景中的光源阴影设置
      if (this.activeScene) {
        const entities = this.activeScene.getEntities();
        entities.forEach((entity: Entity) => {
          const lightComponent = entity.getComponent('Light');
          if (lightComponent && typeof lightComponent.setCastShadow === 'function') {
            lightComponent.setCastShadow(enabled);
          }
        });
      }

      // 发出阴影设置变更事件
      this.emit('shadowsChanged', { enabled });
    } catch (error) {
      console.error('设置阴影失败:', error);
      throw error;
    }
  }

  private async handleSetLighting(args: any[]): Promise<void> {
    const [lightingSettings] = args;
    console.log(`设置光照:`, lightingSettings);

    try {
      // 调用引擎光照系统
      if (this.engine && (this.engine as any).lightingSystem) {
        (this.engine as any).lightingSystem.updateSettings(lightingSettings);
      }

      // 更新场景环境光
      if (lightingSettings.ambientLight && this.activeScene) {
        // 查找或创建环境光
        const entities = this.activeScene.getEntities();
        let ambientLightEntity = entities.find((entity: Entity) =>
          entity.name === 'AmbientLight' || entity.hasComponent('AmbientLight')
        );

        if (!ambientLightEntity) {
          ambientLightEntity = this.activeScene.createEntity('AmbientLight');
          ambientLightEntity.addComponent('AmbientLight');
        }

        const ambientComponent = ambientLightEntity.getComponent('AmbientLight');
        if (ambientComponent) {
          Object.assign(ambientComponent, lightingSettings.ambientLight);
        }
      }

      // 更新方向光
      if (lightingSettings.directionalLight && this.activeScene) {
        const entities = this.activeScene.getEntities();
        let dirLightEntity = entities.find((entity: Entity) =>
          entity.name === 'DirectionalLight' || entity.hasComponent('DirectionalLight')
        );

        if (!dirLightEntity) {
          dirLightEntity = this.activeScene.createEntity('DirectionalLight');
          dirLightEntity.addComponent('DirectionalLight');
        }

        const dirComponent = dirLightEntity.getComponent('DirectionalLight');
        if (dirComponent) {
          Object.assign(dirComponent, lightingSettings.directionalLight);
        }
      }

      // 发出光照设置变更事件
      this.emit('lightingChanged', { lightingSettings });
    } catch (error) {
      console.error('设置光照失败:', error);
      throw error;
    }
  }

  /**
   * 序列化场景为数据
   * @returns 场景数据
   */
  private async serializeScene(): Promise<any> {
    if (!this.activeScene) {
      throw new Error('没有活动场景');
    }

    try {
      const entities = this.activeScene.getEntities();

      const sceneData = {
        id: this.activeScene.id,
        name: this.activeScene.name,
        version: '1.0.0',
        timestamp: Date.now(),
        entities: await Promise.all(entities.map(entity => this.serializeEntity(entity))),
        settings: await this.serializeSceneSettings(),
        environment: await this.serializeEnvironmentSettings()
      };

      return sceneData;
    } catch (error) {
      console.error('序列化场景失败:', error);
      throw error;
    }
  }

  /**
   * 序列化实体
   * @param entity 实体
   * @returns 实体数据
   */
  private async serializeEntity(entity: Entity): Promise<any> {
    const transform = entity.getTransform();
    const components = entity.getComponents();

    const entityData = {
      id: entity.id,
      name: entity.name,
      active: entity.isActive(),
      transform: {
        position: transform ? {
          x: transform.getPosition().x,
          y: transform.getPosition().y,
          z: transform.getPosition().z
        } : { x: 0, y: 0, z: 0 },
        rotation: transform ? {
          x: transform.getRotation().x,
          y: transform.getRotation().y,
          z: transform.getRotation().z
        } : { x: 0, y: 0, z: 0 },
        scale: transform ? {
          x: transform.getScale().x,
          y: transform.getScale().y,
          z: transform.getScale().z
        } : { x: 1, y: 1, z: 1 }
      },
      components: Array.from(components.entries()).map(([type, component]) =>
        this.serializeComponent(type, component)
      )
    };

    return entityData;
  }

  /**
   * 序列化组件
   * @param type 组件类型
   * @param component 组件实例
   * @returns 组件数据
   */
  private serializeComponent(type: string, component: any): any {
    const componentData: any = {
      type,
      enabled: component && 'isEnabled' in component ? component.isEnabled() : true,
      properties: {}
    };

    // 序列化组件属性
    if (component && typeof component === 'object') {
      // 获取可序列化的属性
      const serializableProps = this.getSerializableProperties(component);
      for (const prop of serializableProps) {
        if (prop in component) {
          componentData.properties[prop] = this.serializeValue(component[prop]);
        }
      }
    }

    return componentData;
  }

  /**
   * 获取可序列化的属性列表
   * @param component 组件
   * @returns 属性名数组
   */
  private getSerializableProperties(component: any): string[] {
    // 根据组件类型返回可序列化的属性
    const commonProps = ['enabled', 'visible', 'color', 'material', 'texture'];

    // 这里可以根据具体的组件类型返回不同的属性列表
    if (component.constructor && component.constructor.name) {
      const componentType = component.constructor.name;

      switch (componentType) {
        case 'MeshRenderer':
          return ['material', 'castShadow', 'receiveShadow', 'visible'];
        case 'Light':
          return ['color', 'intensity', 'range', 'spotAngle'];
        case 'Camera':
          return ['fov', 'near', 'far', 'clearColor'];
        case 'AudioSource':
          return ['clip', 'volume', 'loop', 'autoPlay'];
        default:
          return commonProps;
      }
    }

    return commonProps;
  }

  /**
   * 序列化值
   * @param value 值
   * @returns 序列化后的值
   */
  private serializeValue(value: any): any {
    if (value === null || value === undefined) {
      return value;
    }

    // 处理基本类型
    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
      return value;
    }

    // 处理数组
    if (Array.isArray(value)) {
      return value.map(item => this.serializeValue(item));
    }

    // 处理Vector3等特殊对象
    if (value && typeof value === 'object') {
      if ('x' in value && 'y' in value && 'z' in value) {
        return { x: value.x, y: value.y, z: value.z };
      }

      if ('r' in value && 'g' in value && 'b' in value) {
        return { r: value.r, g: value.g, b: value.b, a: value.a || 1 };
      }

      // 处理普通对象
      const serialized: any = {};
      for (const [key, val] of Object.entries(value)) {
        if (typeof val !== 'function') {
          serialized[key] = this.serializeValue(val);
        }
      }
      return serialized;
    }

    return value;
  }

  /**
   * 序列化场景设置
   * @returns 场景设置数据
   */
  private async serializeSceneSettings(): Promise<any> {
    return {
      backgroundColor: '#000000',
      fog: {
        enabled: false,
        color: '#ffffff',
        near: 1,
        far: 1000
      },
      physics: {
        enabled: true,
        gravity: { x: 0, y: -9.81, z: 0 }
      }
    };
  }

  /**
   * 序列化环境设置
   * @returns 环境设置数据
   */
  private async serializeEnvironmentSettings(): Promise<any> {
    return {
      lighting: {
        ambientColor: '#404040',
        ambientIntensity: 0.4
      },
      skybox: {
        type: 'color',
        color: '#87CEEB'
      },
      audio: {
        masterVolume: 1.0,
        musicVolume: 0.8,
        effectsVolume: 1.0
      }
    };
  }

  /**
   * 从数据加载场景内容
   * @param sceneData 场景数据
   */
  private async loadSceneFromData(sceneData: any): Promise<void> {
    if (!this.activeScene) {
      throw new Error('没有活动场景');
    }

    try {
      console.log('开始加载场景数据:', sceneData);

      // 设置场景基本信息
      if (sceneData.name) {
        this.activeScene.name = sceneData.name;
      }

      // 加载实体
      if (sceneData.entities && Array.isArray(sceneData.entities)) {
        for (const entityData of sceneData.entities) {
          await this.loadEntityFromData(entityData);
        }
      }

      // 加载场景设置
      if (sceneData.settings) {
        await this.loadSceneSettings(sceneData.settings);
      }

      // 加载环境设置
      if (sceneData.environment) {
        await this.loadEnvironmentSettings(sceneData.environment);
      }

      console.log('场景数据加载完成');
    } catch (error) {
      console.error('加载场景数据失败:', error);
      throw error;
    }
  }

  /**
   * 从数据加载实体
   * @param entityData 实体数据
   */
  private async loadEntityFromData(entityData: any): Promise<Entity> {
    if (!this.activeScene) {
      throw new Error('没有活动场景');
    }

    const entity = this.activeScene.createEntity(entityData.name || 'Entity');

    // 设置实体属性
    if (entityData.id) {
      entity.id = entityData.id;
    }

    // 加载组件
    if (entityData.components && Array.isArray(entityData.components)) {
      for (const componentData of entityData.components) {
        await this.loadComponentFromData(entity, componentData);
      }
    }

    // 设置变换
    if (entityData.transform) {
      const transform = entity.getTransform();
      if (entityData.transform.position) {
        const pos = entityData.transform.position;
        transform.setPosition(new Vector3(pos.x || 0, pos.y || 0, pos.z || 0));
      }
      if (entityData.transform.rotation) {
        const rot = entityData.transform.rotation;
        transform.setRotation(rot.x || 0, rot.y || 0, rot.z || 0);
      }
      if (entityData.transform.scale) {
        const scale = entityData.transform.scale;
        transform.setScale(new Vector3(scale.x || 1, scale.y || 1, scale.z || 1));
      }
    }

    // 设置父子关系
    if (entityData.parentId) {
      const parentEntity = this.findEntityById(entityData.parentId);
      if (parentEntity) {
        entity.setParent(parentEntity);
      }
    }

    return entity;
  }

  /**
   * 从数据加载组件
   * @param entity 实体
   * @param componentData 组件数据
   */
  private async loadComponentFromData(entity: Entity, componentData: any): Promise<void> {
    const component = entity.addComponent(componentData.type);

    // 设置组件属性
    if (componentData.properties) {
      for (const [key, value] of Object.entries(componentData.properties)) {
        if (component && typeof component === 'object' && key in component) {
          (component as any)[key] = value;
        }
      }
    }

    // 设置组件启用状态
    if (typeof componentData.enabled === 'boolean' && component && 'setEnabled' in component) {
      (component as any).setEnabled(componentData.enabled);
    }
  }

  /**
   * 加载场景设置
   * @param settings 场景设置
   */
  private async loadSceneSettings(settings: any): Promise<void> {
    console.log('加载场景设置:', settings);

    // 设置背景颜色
    if (settings.backgroundColor) {
      // 这里可以设置场景背景颜色
      console.log('设置背景颜色:', settings.backgroundColor);
    }

    // 设置雾效
    if (settings.fog) {
      console.log('设置雾效:', settings.fog);
    }

    // 设置物理参数
    if (settings.physics) {
      console.log('设置物理参数:', settings.physics);
    }
  }

  /**
   * 加载环境设置
   * @param environment 环境设置
   */
  private async loadEnvironmentSettings(environment: any): Promise<void> {
    console.log('加载环境设置:', environment);

    // 设置光照
    if (environment.lighting) {
      console.log('设置光照:', environment.lighting);
    }

    // 设置天空盒
    if (environment.skybox) {
      console.log('设置天空盒:', environment.skybox);
    }

    // 设置环境音效
    if (environment.audio) {
      console.log('设置环境音效:', environment.audio);
    }
  }

  /**
   * 设置语言
   * @param language 语言代码 (如 'zh-CN', 'en-US')
   */
  public setLanguage(language: string): void {
    if (this.engine && this.engine.getI18n && typeof this.engine.getI18n().setLanguage === 'function') {
      this.engine.getI18n().setLanguage(language);
    }
  }

  /**
   * 获取当前语言
   * @returns 当前语言代码
   */
  public getLanguage(): string {
    if (this.engine && this.engine.getI18n && typeof this.engine.getI18n().getLanguage === 'function') {
      return this.engine.getI18n().getLanguage();
    }
    return 'zh-CN';
  }

  /**
   * 翻译文本
   * @param key 翻译键
   * @param params 参数
   * @returns 翻译后的文本
   */
  public translate(key: string, params?: any): string {
    if (this.engine && this.engine.getI18n && typeof this.engine.getI18n().translate === 'function') {
      return this.engine.getI18n().translate(key, params);
    }
    return key;
  }

  /**
   * 启用调试模式
   * @param enabled 是否启用
   */
  public setDebugMode(enabled: boolean): void {
    if (this.engine && typeof this.engine.setDebug === 'function') {
      this.engine.setDebug(enabled);
    }
  }

  /**
   * 是否处于调试模式
   * @returns 是否调试模式
   */
  public isDebugMode(): boolean {
    if (this.engine && typeof this.engine.isDebug === 'function') {
      return this.engine.isDebug();
    }
    return false;
  }

  /**
   * 获取引擎性能信息
   * @returns 性能信息
   */
  public getPerformanceInfo(): any {
    const timeInfo = this.getTime();
    return {
      fps: timeInfo.getFPS(),
      frameCount: timeInfo.getFrameCount(),
      deltaTime: timeInfo.getDeltaTime(),
      timeScale: timeInfo.getTimeScale(),
      entityCount: this.activeScene ? this.activeScene.getEntities().length : 0,
      systemCount: this.engine ? this.engine.systems.length : 0,
      memoryUsage: (performance as any).memory ? {
        usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
        totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
        jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
      } : null
    };
  }

  /**
   * 获取引擎状态信息
   * @returns 状态信息
   */
  public getEngineStatus(): any {
    return {
      initialized: this.engine !== null,
      running: this.engine ? this.engine.isRunning() : false,
      activeScene: this.activeScene ? this.activeScene.name : null,
      activeCamera: this.activeCamera !== null,
      selectedEntities: this.selectedEntities.length,
      systems: {
        physics: this.physicsSystem !== null,
        animation: this.animationSystem !== null,
        audio: this.audioSystem !== null,
        input: this.inputSystem !== null,
        network: this.networkSystem !== null,
        ui: this.uiSystem !== null,
        material: this.materialSystem !== null,
        render: this.renderSystem !== null
      },
      managers: {
        asset: this.assetManager !== null,
        scene: this.sceneManager !== null
      }
    };
  }

  /**
   * 重置引擎服务
   */
  public reset(): void {
    // 停止引擎
    if (this.engine && this.engine.isRunning()) {
      this.engine.stop();
    }

    // 清空场景
    if (this.activeScene) {
      this.activeScene.clear();
    }

    // 清空选择
    this.clearSelection();

    // 重置变换模式
    this.transformMode = TransformMode.TRANSLATE;
    this.transformSpace = TransformSpace.LOCAL;

    console.log('引擎服务已重置');
  }

  /**
   * 获取系统信息
   * @param systemType 系统类型
   * @returns 系统信息
   */
  public getSystemInfo(systemType: string): any {
    const systemMap: { [key: string]: any } = {
      'physics': this.physicsSystem,
      'animation': this.animationSystem,
      'audio': this.audioSystem,
      'input': this.inputSystem,
      'network': this.networkSystem,
      'ui': this.uiSystem,
      'material': this.materialSystem,
      'render': this.renderSystem
    };

    const system = systemMap[systemType];
    if (!system) {
      return null;
    }

    return {
      type: systemType,
      enabled: typeof system.isEnabled === 'function' ? system.isEnabled() : true,
      priority: typeof system.getPriority === 'function' ? system.getPriority() : 0,
      initialized: system !== null
    };
  }

  /**
   * 根据ID查找实体
   * @param id 实体ID
   * @returns 实体或null
   */
  private findEntityById(id: string): Entity | null {
    if (!this.activeScene) {
      return null;
    }

    const entities = this.activeScene.getEntities();
    return entities.find((entity: Entity) => entity.id === id) || null;
  }
}

export default EngineService.getInstance();
